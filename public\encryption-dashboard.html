<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Encryption System Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #ffffff;
            padding: 20px;
            margin: 0;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #00ff99;
            text-shadow: 0 0 10px rgba(0, 255, 153, 0.3);
            margin-bottom: 30px;
        }
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(0, 255, 153, 0.3);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        .card:hover {
            border-color: #00ff99;
            box-shadow: 0 0 20px rgba(0, 255, 153, 0.2);
        }
        .card h3 {
            color: #00ff99;
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        .status-success { background: #00ff99; }
        .status-error { background: #ff4757; }
        .status-warning { background: #ffa502; }
        .status-loading { background: #3742fa; animation: pulse 1.5s infinite; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        button {
            background: linear-gradient(45deg, #00ff99, #00cc7a);
            color: #1a1a2e;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 5px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 153, 0.3);
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        .test-results {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            max-height: 300px;
            overflow-y: auto;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .test-success { color: #00ff99; }
        .test-error { color: #ff4757; }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .stat-card {
            background: rgba(0, 255, 153, 0.1);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #00ff99;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #3742fa;
        }
        .error {
            color: #ff4757;
            background: rgba(255, 71, 87, 0.1);
            border: 1px solid rgba(255, 71, 87, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        pre {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            padding: 15px;
            overflow-x: auto;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Encryption System Dashboard</h1>
        
        <div class="dashboard-grid">
            <!-- Environment Status -->
            <div class="card">
                <h3>
                    <span class="status-indicator status-loading" id="envStatus"></span>
                    Environment Status
                </h3>
                <div id="envInfo">
                    <div class="loading">Loading environment info...</div>
                </div>
                <button onclick="checkEnvironment()">Refresh Environment</button>
            </div>
            
            <!-- Basic Encryption Test -->
            <div class="card">
                <h3>
                    <span class="status-indicator status-loading" id="basicTestStatus"></span>
                    Basic Encryption Test
                </h3>
                <div id="basicTestResults">
                    <div class="loading">Ready to test...</div>
                </div>
                <button onclick="runBasicTest()">Run Basic Test</button>
            </div>
            
            <!-- Comprehensive Validation -->
            <div class="card">
                <h3>
                    <span class="status-indicator status-loading" id="validationStatus"></span>
                    Comprehensive Validation
                </h3>
                <div id="validationResults">
                    <div class="loading">Ready to validate...</div>
                </div>
                <button onclick="runValidation()">Run Full Validation</button>
            </div>
        </div>
        
        <!-- Summary Statistics -->
        <div class="card">
            <h3>📊 System Summary</h3>
            <div class="summary-stats" id="summaryStats">
                <div class="stat-card">
                    <div class="stat-number" id="totalTests">-</div>
                    <div class="stat-label">Total Tests</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="passedTests">-</div>
                    <div class="stat-label">Passed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="failedTests">-</div>
                    <div class="stat-label">Failed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="successRate">-</div>
                    <div class="stat-label">Success Rate</div>
                </div>
            </div>
        </div>
        
        <!-- Auto-run on load -->
        <div style="text-align: center; margin-top: 30px;">
            <button onclick="runAllTests()" style="font-size: 1.2em; padding: 15px 30px;">
                🚀 Run All Tests
            </button>
        </div>
    </div>

    <script>
        // Auto-run environment check on load
        window.addEventListener('load', () => {
            checkEnvironment();
        });

        async function checkEnvironment() {
            const statusEl = document.getElementById('envStatus');
            const infoEl = document.getElementById('envInfo');
            
            statusEl.className = 'status-indicator status-loading';
            infoEl.innerHTML = '<div class="loading">Checking environment...</div>';
            
            try {
                const response = await fetch('/api/debug-env');
                const data = await response.json();
                
                if (data.success) {
                    statusEl.className = 'status-indicator status-success';
                    infoEl.innerHTML = `
                        <div class="test-results">
                            <div class="test-item">
                                <span>Supabase URL</span>
                                <span class="test-success">✅ ${data.environment.NEXT_PUBLIC_SUPABASE_URL ? 'Present' : 'Missing'}</span>
                            </div>
                            <div class="test-item">
                                <span>Service Role Key</span>
                                <span class="test-success">✅ ${data.environment.SUPABASE_SERVICE_ROLE_KEY}</span>
                            </div>
                            <div class="test-item">
                                <span>Encryption Key</span>
                                <span class="test-success">✅ ${data.environment.ENCRYPTION_KEY}</span>
                            </div>
                            <div class="test-item">
                                <span>Environment</span>
                                <span class="test-success">✅ ${data.environment.NODE_ENV}</span>
                            </div>
                        </div>
                    `;
                } else {
                    statusEl.className = 'status-indicator status-error';
                    infoEl.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                }
            } catch (error) {
                statusEl.className = 'status-indicator status-error';
                infoEl.innerHTML = `<div class="error">❌ Failed to check environment: ${error.message}</div>`;
            }
        }

        async function runBasicTest() {
            const statusEl = document.getElementById('basicTestStatus');
            const resultsEl = document.getElementById('basicTestResults');
            
            statusEl.className = 'status-indicator status-loading';
            resultsEl.innerHTML = '<div class="loading">Running basic encryption test...</div>';
            
            try {
                const response = await fetch('/api/test-encryption');
                const data = await response.json();
                
                if (data.success) {
                    statusEl.className = 'status-indicator status-success';
                    resultsEl.innerHTML = `
                        <div class="test-results">
                            ${data.tests.map(test => `
                                <div class="test-item">
                                    <span>${test.name}</span>
                                    <span class="${test.success ? 'test-success' : 'test-error'}">
                                        ${test.success ? '✅' : '❌'} ${test.success ? 'PASS' : 'FAIL'}
                                    </span>
                                </div>
                            `).join('')}
                        </div>
                        <div style="margin-top: 10px; color: #00ff99;">
                            ✅ ${data.passedTests}/${data.totalTests} tests passed
                        </div>
                    `;
                } else {
                    statusEl.className = 'status-indicator status-error';
                    resultsEl.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                }
            } catch (error) {
                statusEl.className = 'status-indicator status-error';
                resultsEl.innerHTML = `<div class="error">❌ Test failed: ${error.message}</div>`;
            }
        }

        async function runValidation() {
            const statusEl = document.getElementById('validationStatus');
            const resultsEl = document.getElementById('validationResults');
            
            statusEl.className = 'status-indicator status-loading';
            resultsEl.innerHTML = '<div class="loading">Running comprehensive validation...</div>';
            
            try {
                const response = await fetch('/api/encryption-validation');
                const data = await response.json();
                
                if (data.success) {
                    statusEl.className = 'status-indicator status-success';
                    
                    // Update summary stats
                    document.getElementById('totalTests').textContent = data.summary.totalTests;
                    document.getElementById('passedTests').textContent = data.summary.passedTests;
                    document.getElementById('failedTests').textContent = data.summary.failedTests;
                    document.getElementById('successRate').textContent = data.summary.successRate;
                    
                    resultsEl.innerHTML = `
                        <div class="test-results">
                            ${data.validationResults.slice(0, 10).map(result => `
                                <div class="test-item">
                                    <span>${result.test} (${result.input})</span>
                                    <span class="${result.success ? 'test-success' : 'test-error'}">
                                        ${result.success ? '✅' : '❌'} ${result.success ? 'PASS' : 'FAIL'}
                                    </span>
                                </div>
                            `).join('')}
                            ${data.validationResults.length > 10 ? `<div style="text-align: center; padding: 10px; opacity: 0.7;">... and ${data.validationResults.length - 10} more tests</div>` : ''}
                        </div>
                        <div style="margin-top: 10px; color: #00ff99;">
                            ✅ ${data.summary.passedTests}/${data.summary.totalTests} tests passed (${data.summary.successRate})
                        </div>
                    `;
                } else {
                    statusEl.className = 'status-indicator status-error';
                    resultsEl.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                }
            } catch (error) {
                statusEl.className = 'status-indicator status-error';
                resultsEl.innerHTML = `<div class="error">❌ Validation failed: ${error.message}</div>`;
            }
        }

        async function runAllTests() {
            await checkEnvironment();
            await runBasicTest();
            await runValidation();
        }
    </script>
</body>
</html>
