{"version": 2, "buildCommand": "npm run build", "framework": "nextjs", "redirects": [{"source": "/(.*)", "has": [{"type": "host", "value": "gurusingapore.com"}], "destination": "https://www.gurusingapore.com/$1", "permanent": true}], "headers": [{"source": "/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "https://www.gurusingapore.com"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Requested-With"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}]}, {"source": "/auth/(.*)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}], "env": {"NODE_ENV": "production"}}