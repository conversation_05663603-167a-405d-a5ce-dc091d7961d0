<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Debug Upload API</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff99;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #00ff99;
            text-align: center;
        }
        .section {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        button {
            background: #004d00;
            color: #00ff99;
            border: 1px solid #00ff99;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #006600;
        }
        button:disabled {
            background: #333;
            color: #666;
            cursor: not-allowed;
        }
        input[type="file"] {
            background: #333;
            color: #00ff99;
            border: 1px solid #666;
            padding: 10px;
            border-radius: 5px;
            width: 100%;
            margin: 10px 0;
        }
        .log {
            background: #000;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .success { color: #00ff99; }
        .error { color: #ff4444; }
        .info { color: #4499ff; }
        .warning { color: #ffaa00; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Upload API</h1>
        
        <div class="section">
            <h3>📋 Environment Check</h3>
            <button onclick="checkEnvironment()">Check Environment Variables</button>
            <div id="envResult" class="log"></div>
        </div>

        <div class="section">
            <h3>🧪 Upload Test</h3>
            <input type="file" id="fileInput" accept="image/*">
            <button onclick="debugUpload()" id="uploadBtn">Debug Upload</button>
            <div id="uploadResult" class="log"></div>
        </div>

        <div class="section">
            <h3>📊 Full Debug Log</h3>
            <button onclick="clearLog()">Clear Log</button>
            <div id="debugLog" class="log"></div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const timestamp = new Date().toISOString();
            const logElement = document.getElementById('debugLog');
            const className = type;
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
            document.getElementById('envResult').innerHTML = '';
            document.getElementById('uploadResult').innerHTML = '';
        }

        async function checkEnvironment() {
            log('🔍 Checking environment variables...', 'info');
            const envElement = document.getElementById('envResult');
            
            try {
                const response = await fetch('/api/debug-env');
                const data = await response.json();
                
                log(`Environment check response: ${JSON.stringify(data, null, 2)}`, 'info');
                
                envElement.innerHTML = `<span class="success">✅ Environment Variables:</span>\n${JSON.stringify(data, null, 2)}`;
                
                if (!data.supabaseUrl || data.supabaseUrl !== 'Present') {
                    log('❌ SUPABASE_URL missing!', 'error');
                }
                if (!data.serviceRoleKey || data.serviceRoleKey !== 'Present') {
                    log('❌ SERVICE_ROLE_KEY missing!', 'error');
                }
                
            } catch (error) {
                log(`❌ Environment check failed: ${error.message}`, 'error');
                envElement.innerHTML = `<span class="error">❌ Failed to check environment: ${error.message}</span>`;
            }
        }

        async function debugUpload() {
            const fileInput = document.getElementById('fileInput');
            const uploadBtn = document.getElementById('uploadBtn');
            const resultElement = document.getElementById('uploadResult');
            
            const file = fileInput.files[0];
            if (!file) {
                log('❌ No file selected', 'error');
                resultElement.innerHTML = '<span class="error">❌ Please select a file first</span>';
                return;
            }

            log(`📁 Selected file: ${file.name} (${file.size} bytes, ${file.type})`, 'info');
            
            uploadBtn.disabled = true;
            uploadBtn.textContent = '⏳ Debugging...';
            resultElement.innerHTML = '<span class="info">⏳ Starting debug upload...</span>';

            try {
                const formData = new FormData();
                formData.append('file', file);
                
                log('📤 Sending upload request to /api/upload...', 'info');
                log(`📋 FormData contents: file=${file.name}`, 'info');
                
                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData,
                });
                
                log(`📡 Response received:`, 'info');
                log(`  - Status: ${response.status}`, 'info');
                log(`  - Status Text: ${response.statusText}`, 'info');
                log(`  - OK: ${response.ok}`, 'info');
                log(`  - URL: ${response.url}`, 'info');
                
                // Log all response headers
                const headers = {};
                for (const [key, value] of response.headers.entries()) {
                    headers[key] = value;
                }
                log(`  - Headers: ${JSON.stringify(headers, null, 2)}`, 'info');
                
                // Get response text first
                const responseText = await response.text();
                log(`📄 Raw response text: ${responseText}`, 'info');
                
                let responseData;
                try {
                    responseData = JSON.parse(responseText);
                    log(`📊 Parsed response data: ${JSON.stringify(responseData, null, 2)}`, 'info');
                } catch (parseError) {
                    log(`❌ Failed to parse response as JSON: ${parseError.message}`, 'error');
                    responseData = { error: 'Invalid JSON response', rawResponse: responseText };
                }
                
                if (response.ok) {
                    log('✅ Upload successful!', 'success');
                    resultElement.innerHTML = `<span class="success">✅ Upload successful!</span>\n${JSON.stringify(responseData, null, 2)}`;
                } else {
                    log(`❌ Upload failed with status ${response.status}`, 'error');
                    resultElement.innerHTML = `<span class="error">❌ Upload failed:</span>\n${JSON.stringify(responseData, null, 2)}`;
                }
                
            } catch (error) {
                log(`🚨 Upload error: ${error.message}`, 'error');
                log(`🚨 Error stack: ${error.stack}`, 'error');
                resultElement.innerHTML = `<span class="error">🚨 Upload error: ${error.message}</span>`;
            } finally {
                uploadBtn.disabled = false;
                uploadBtn.textContent = 'Debug Upload';
            }
        }

        // Auto-check environment on page load
        window.onload = function() {
            log('🚀 Debug page loaded', 'info');
            checkEnvironment();
        };
    </script>
</body>
</html>
