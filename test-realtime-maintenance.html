<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Realtime Maintenance</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #0a0e13; color: #fff; }
        .container { max-width: 800px; margin: 0 auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .active { background: #00ff9920; border: 1px solid #00ff99; }
        .maintenance { background: #f59e0b20; border: 1px solid #f59e0b; }
        button { background: #00ff99; color: #000; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #00cc7a; }
        .log { background: #1a1f2e; padding: 10px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Realtime Maintenance Test</h1>
        <p>This page tests the realtime functionality of the maintenance system.</p>
        
        <div id="status">Loading...</div>
        
        <div>
            <button onclick="toggleHomepage()">Toggle Homepage Maintenance</button>
            <button onclick="toggleCasinos()">Toggle Casinos Maintenance</button>
            <button onclick="refreshData()">Refresh Data</button>
        </div>
        
        <div id="log" class="log">
            <strong>Realtime Log:</strong><br>
            Connecting to Supabase...<br>
        </div>
    </div>

    <script>
        // Initialize Supabase client
        const supabaseUrl = 'YOUR_SUPABASE_URL';
        const supabaseKey = 'YOUR_SUPABASE_ANON_KEY';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
        
        let pages = [];
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        async function loadPages() {
            try {
                const { data, error } = await supabase
                    .from('page_maintenance')
                    .select('*')
                    .order('page_name');
                
                if (error) throw error;
                
                pages = data || [];
                updateDisplay();
                log(`✅ Loaded ${pages.length} pages`);
            } catch (error) {
                log(`❌ Error loading pages: ${error.message}`);
            }
        }
        
        function updateDisplay() {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = pages.map(page => `
                <div class="status ${page.is_maintenance ? 'maintenance' : 'active'}">
                    <strong>${page.page_name}</strong> (${page.page_path})<br>
                    Status: ${page.is_maintenance ? '🔧 Maintenance' : '✅ Active'}<br>
                    Message: ${page.maintenance_message}
                </div>
            `).join('');
        }
        
        async function toggleHomepage() {
            await togglePage('/', true);
        }
        
        async function toggleCasinos() {
            await togglePage('/casinos', true);
        }
        
        async function togglePage(path, newStatus) {
            try {
                const page = pages.find(p => p.page_path === path);
                if (!page) {
                    log(`❌ Page ${path} not found`);
                    return;
                }
                
                const { error } = await supabase
                    .from('page_maintenance')
                    .update({ 
                        is_maintenance: !page.is_maintenance,
                        updated_at: new Date().toISOString()
                    })
                    .eq('page_path', path);
                
                if (error) throw error;
                
                log(`🔄 Toggled ${path} maintenance: ${!page.is_maintenance ? 'ON' : 'OFF'}`);
            } catch (error) {
                log(`❌ Error toggling ${path}: ${error.message}`);
            }
        }
        
        function refreshData() {
            log('🔄 Manual refresh requested');
            loadPages();
        }
        
        // Setup realtime subscription
        function setupRealtime() {
            const channel = supabase
                .channel('page_maintenance_changes')
                .on(
                    'postgres_changes',
                    {
                        event: '*',
                        schema: 'public',
                        table: 'page_maintenance'
                    },
                    (payload) => {
                        log(`🔄 Realtime update: ${payload.eventType} - ${JSON.stringify(payload.new || payload.old)}`);
                        loadPages(); // Refresh data when changes occur
                    }
                )
                .subscribe((status) => {
                    log(`📡 Realtime subscription status: ${status}`);
                });
            
            return channel;
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', async () => {
            log('🚀 Initializing realtime test...');
            
            // Load initial data
            await loadPages();
            
            // Setup realtime
            const channel = setupRealtime();
            
            log('✅ Realtime test ready!');
            log('💡 Try opening /admin/maintenance in another tab and toggle maintenance mode');
        });
    </script>
</body>
</html>
