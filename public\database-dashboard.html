<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🗃️ Database Operations Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: #ffffff;
            padding: 20px;
            margin: 0;
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #3498db;
            text-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
            margin-bottom: 30px;
        }
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(52, 152, 219, 0.3);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        .card:hover {
            border-color: #3498db;
            box-shadow: 0 0 20px rgba(52, 152, 219, 0.2);
        }
        .card h3 {
            color: #3498db;
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        .status-success { background: #27ae60; }
        .status-error { background: #e74c3c; }
        .status-warning { background: #f39c12; }
        .status-loading { background: #3498db; animation: pulse 1.5s infinite; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        button {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 5px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        .test-results {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            max-height: 400px;
            overflow-y: auto;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .test-success { color: #27ae60; }
        .test-error { color: #e74c3c; }
        .test-warning { color: #f39c12; }
        .performance-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .stat-card {
            background: rgba(52, 152, 219, 0.1);
            border-radius: 8px;
            padding: 12px;
            text-align: center;
        }
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #3498db;
        }
        .stat-label {
            font-size: 0.8em;
            opacity: 0.8;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #3498db;
        }
        .error {
            color: #e74c3c;
            background: rgba(231, 76, 60, 0.1);
            border: 1px solid rgba(231, 76, 60, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .operation-details {
            font-size: 0.9em;
            opacity: 0.8;
            margin-top: 5px;
        }
        .wide-card {
            grid-column: 1 / -1;
        }
        .casino-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .casino-card {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 6px;
            padding: 10px;
            font-size: 0.9em;
        }
        .casino-name {
            font-weight: bold;
            color: #3498db;
        }
        .casino-rating {
            color: #f39c12;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗃️ Database Operations Dashboard</h1>
        
        <div class="dashboard-grid">
            <!-- Database Connection Status -->
            <div class="card">
                <h3>
                    <span class="status-indicator status-loading" id="dbStatus"></span>
                    Database Connection
                </h3>
                <div id="dbInfo">
                    <div class="loading">Checking database connection...</div>
                </div>
                <button onclick="testDatabaseConnection()">Test Connection</button>
            </div>
            
            <!-- Basic Operations Test -->
            <div class="card">
                <h3>
                    <span class="status-indicator status-loading" id="basicTestStatus"></span>
                    Basic Operations
                </h3>
                <div id="basicTestResults">
                    <div class="loading">Ready to test...</div>
                </div>
                <button onclick="runBasicOperations()">Run Basic Tests</button>
            </div>
            
            <!-- Performance Monitoring -->
            <div class="card">
                <h3>
                    <span class="status-indicator status-loading" id="performanceStatus"></span>
                    Performance Metrics
                </h3>
                <div id="performanceResults">
                    <div class="loading">Ready to monitor...</div>
                </div>
                <button onclick="runPerformanceTest()">Run Performance Test</button>
            </div>
        </div>
        
        <!-- Comprehensive Database Test -->
        <div class="card wide-card">
            <h3>
                <span class="status-indicator status-loading" id="comprehensiveStatus"></span>
                Comprehensive Database Test
            </h3>
            <div id="comprehensiveResults">
                <div class="loading">Ready for comprehensive testing...</div>
            </div>
            <div style="text-align: center; margin-top: 15px;">
                <button onclick="runComprehensiveTest()" style="font-size: 1.1em; padding: 15px 30px;">
                    🚀 Run All Database Tests
                </button>
            </div>
        </div>
        
        <!-- Casino Data Preview -->
        <div class="card wide-card">
            <h3>📊 Live Casino Data Preview</h3>
            <div id="casinoPreview">
                <div class="loading">Loading casino data...</div>
            </div>
            <button onclick="loadCasinoPreview()">Refresh Casino Data</button>
        </div>
    </div>

    <script>
        // Auto-load on page load
        window.addEventListener('load', () => {
            testDatabaseConnection();
            loadCasinoPreview();
        });

        async function testDatabaseConnection() {
            const statusEl = document.getElementById('dbStatus');
            const infoEl = document.getElementById('dbInfo');
            
            statusEl.className = 'status-indicator status-loading';
            infoEl.innerHTML = '<div class="loading">Testing database connection...</div>';
            
            try {
                const response = await fetch('/api/debug-env');
                const data = await response.json();
                
                if (data.success && data.environment.NEXT_PUBLIC_SUPABASE_URL) {
                    statusEl.className = 'status-indicator status-success';
                    infoEl.innerHTML = `
                        <div class="test-results">
                            <div class="test-item">
                                <span>Supabase URL</span>
                                <span class="test-success">✅ Connected</span>
                            </div>
                            <div class="test-item">
                                <span>Service Role Key</span>
                                <span class="test-success">✅ ${data.environment.SUPABASE_SERVICE_ROLE_KEY}</span>
                            </div>
                            <div class="test-item">
                                <span>Environment</span>
                                <span class="test-success">✅ ${data.environment.NODE_ENV}</span>
                            </div>
                        </div>
                    `;
                } else {
                    statusEl.className = 'status-indicator status-error';
                    infoEl.innerHTML = `<div class="error">❌ Database connection failed</div>`;
                }
            } catch (error) {
                statusEl.className = 'status-indicator status-error';
                infoEl.innerHTML = `<div class="error">❌ Connection error: ${error.message}</div>`;
            }
        }

        async function runBasicOperations() {
            const statusEl = document.getElementById('basicTestStatus');
            const resultsEl = document.getElementById('basicTestResults');
            
            statusEl.className = 'status-indicator status-loading';
            resultsEl.innerHTML = '<div class="loading">Running basic database operations...</div>';
            
            try {
                const startTime = Date.now();
                const response = await fetch('/api/test-database');
                const endTime = Date.now();
                const data = await response.json();
                
                if (data.success) {
                    statusEl.className = 'status-indicator status-success';
                    resultsEl.innerHTML = `
                        <div class="performance-stats">
                            <div class="stat-card">
                                <div class="stat-number">${data.summary.totalTests}</div>
                                <div class="stat-label">Total Tests</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${data.summary.passedTests}</div>
                                <div class="stat-label">Passed</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${endTime - startTime}ms</div>
                                <div class="stat-label">Response Time</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${data.summary.successRate}</div>
                                <div class="stat-label">Success Rate</div>
                            </div>
                        </div>
                        <div style="margin-top: 10px; color: #27ae60;">
                            ✅ All basic operations working correctly
                        </div>
                    `;
                } else {
                    statusEl.className = 'status-indicator status-error';
                    resultsEl.innerHTML = `<div class="error">❌ ${data.message}</div>`;
                }
            } catch (error) {
                statusEl.className = 'status-indicator status-error';
                resultsEl.innerHTML = `<div class="error">❌ Test failed: ${error.message}</div>`;
            }
        }

        async function runPerformanceTest() {
            const statusEl = document.getElementById('performanceStatus');
            const resultsEl = document.getElementById('performanceResults');
            
            statusEl.className = 'status-indicator status-loading';
            resultsEl.innerHTML = '<div class="loading">Running performance tests...</div>';
            
            try {
                const tests = [];
                
                // Test multiple operations for performance
                for (let i = 0; i < 5; i++) {
                    const startTime = Date.now();
                    await fetch('/api/test-database');
                    const endTime = Date.now();
                    tests.push(endTime - startTime);
                }
                
                const avgTime = Math.round(tests.reduce((a, b) => a + b, 0) / tests.length);
                const minTime = Math.min(...tests);
                const maxTime = Math.max(...tests);
                
                statusEl.className = 'status-indicator status-success';
                resultsEl.innerHTML = `
                    <div class="performance-stats">
                        <div class="stat-card">
                            <div class="stat-number">${avgTime}ms</div>
                            <div class="stat-label">Avg Response</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${minTime}ms</div>
                            <div class="stat-label">Min Response</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${maxTime}ms</div>
                            <div class="stat-label">Max Response</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${tests.length}</div>
                            <div class="stat-label">Test Runs</div>
                        </div>
                    </div>
                    <div style="margin-top: 10px; color: #27ae60;">
                        ✅ Performance metrics collected successfully
                    </div>
                `;
            } catch (error) {
                statusEl.className = 'status-indicator status-error';
                resultsEl.innerHTML = `<div class="error">❌ Performance test failed: ${error.message}</div>`;
            }
        }

        async function runComprehensiveTest() {
            const statusEl = document.getElementById('comprehensiveStatus');
            const resultsEl = document.getElementById('comprehensiveResults');
            
            statusEl.className = 'status-indicator status-loading';
            resultsEl.innerHTML = '<div class="loading">Running comprehensive database tests...</div>';
            
            try {
                const response = await fetch('/api/test-database');
                const data = await response.json();
                
                if (data.success) {
                    statusEl.className = 'status-indicator status-success';
                    resultsEl.innerHTML = `
                        <div class="test-results">
                            ${data.testResults.map(test => `
                                <div class="test-item">
                                    <div>
                                        <strong>${test.test}</strong>
                                        ${test.details ? `<div class="operation-details">${JSON.stringify(test.details.count || test.details.responseTime || 'OK')}</div>` : ''}
                                    </div>
                                    <span class="${test.success ? 'test-success' : 'test-error'}">
                                        ${test.success ? '✅ PASS' : '❌ FAIL'}
                                    </span>
                                </div>
                            `).join('')}
                        </div>
                        <div style="margin-top: 15px; color: #27ae60; text-align: center;">
                            🎉 ${data.summary.passedTests}/${data.summary.totalTests} tests passed (${data.summary.successRate})
                        </div>
                    `;
                } else {
                    statusEl.className = 'status-indicator status-error';
                    resultsEl.innerHTML = `<div class="error">❌ ${data.message}</div>`;
                }
            } catch (error) {
                statusEl.className = 'status-indicator status-error';
                resultsEl.innerHTML = `<div class="error">❌ Comprehensive test failed: ${error.message}</div>`;
            }
        }

        async function loadCasinoPreview() {
            const previewEl = document.getElementById('casinoPreview');
            previewEl.innerHTML = '<div class="loading">Loading casino data...</div>';
            
            try {
                const response = await fetch('/api/test-database');
                const data = await response.json();
                
                if (data.success && data.testResults) {
                    const casinoTest = data.testResults.find(test => test.test === 'Basic Casino Retrieval');
                    if (casinoTest && casinoTest.details && casinoTest.details.firstCasino) {
                        const casino = casinoTest.details.firstCasino;
                        previewEl.innerHTML = `
                            <div class="casino-preview">
                                <div class="casino-card">
                                    <div class="casino-name">${casino.name}</div>
                                    <div class="casino-rating">⭐ ${casino.rating}/5</div>
                                    <div>ID: ${casino.id}</div>
                                    <div>Features: ${casino.hasFeatures ? '✅' : '❌'}</div>
                                    <div>Badges: ${casino.hasBadges ? '✅' : '❌'}</div>
                                </div>
                            </div>
                            <div style="margin-top: 10px; color: #27ae60;">
                                ✅ Casino data loaded successfully (${casinoTest.details.count} casinos found)
                            </div>
                        `;
                    } else {
                        previewEl.innerHTML = '<div class="error">❌ No casino data found</div>';
                    }
                } else {
                    previewEl.innerHTML = '<div class="error">❌ Failed to load casino data</div>';
                }
            } catch (error) {
                previewEl.innerHTML = `<div class="error">❌ Error loading casino data: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
