<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Production Upload Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: white;
            line-height: 1.6;
        }
        .container {
            background: rgba(42, 42, 42, 0.9);
            padding: 30px;
            border-radius: 15px;
            border: 1px solid #444;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        h1 { color: #00ff99; text-align: center; margin-bottom: 30px; }
        .test-section {
            background: #333;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #00ff99;
        }
        input[type="file"] {
            width: 100%;
            padding: 12px;
            background: #444;
            border: 2px dashed #666;
            color: white;
            border-radius: 8px;
            margin: 10px 0;
        }
        button {
            background: linear-gradient(45deg, #00ff99, #00cc77);
            color: black;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        button:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0, 255, 153, 0.3); }
        button:disabled { background: #666; cursor: not-allowed; transform: none; }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            word-break: break-all;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .success { background: rgba(0, 255, 153, 0.1); border: 1px solid #00ff99; color: #00ff99; }
        .error { background: rgba(255, 68, 68, 0.1); border: 1px solid #ff4444; color: #ff6b6b; }
        .loading { background: rgba(255, 193, 7, 0.1); border: 1px solid #ffc107; color: #ffc107; }
        .info { background: rgba(33, 150, 243, 0.1); border: 1px solid #2196f3; color: #64b5f6; }
        .logs { background: #222; padding: 15px; border-radius: 8px; max-height: 300px; overflow-y: auto; }
        .log-entry { margin: 5px 0; padding: 5px; border-radius: 4px; }
        .log-success { background: rgba(0, 255, 153, 0.1); }
        .log-error { background: rgba(255, 68, 68, 0.1); }
        .log-info { background: rgba(33, 150, 243, 0.1); }
        pre { white-space: pre-wrap; margin: 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Production Upload Test</h1>
        
        <div class="test-section">
            <h3>📡 API Status Check</h3>
            <div id="apiStatus">Checking API status...</div>
        </div>
        
        <div class="test-section">
            <h3>📤 File Upload Test</h3>
            <input type="file" id="fileInput" accept="image/*">
            <button onclick="testUpload()" id="uploadBtn">🚀 Test Upload</button>
            <div id="uploadResult"></div>
        </div>
        
        <div class="test-section">
            <h3>📋 Debug Logs</h3>
            <div id="logs" class="logs"></div>
        </div>
    </div>

    <script>
        let logCount = 0;
        
        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${message}`;
            logs.appendChild(entry);
            logs.scrollTop = logs.scrollHeight;
            console.log(`[${type.toUpperCase()}]`, message);
        }
        
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = `result ${type}`;
        }
        
        async function checkAPI() {
            log('🔍 Checking API endpoint...', 'info');
            try {
                const response = await fetch('/api/upload', { method: 'OPTIONS' });
                log(`API OPTIONS response: ${response.status}`, 'info');
                
                if (response.status === 200 || response.status === 405) {
                    showResult('apiStatus', '✅ API endpoint is available', 'success');
                    log('✅ API endpoint is working', 'success');
                } else {
                    showResult('apiStatus', `⚠️ API returned status: ${response.status}`, 'error');
                    log(`⚠️ Unexpected API status: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('apiStatus', `❌ API not reachable: ${error.message}`, 'error');
                log(`❌ API check failed: ${error.message}`, 'error');
            }
        }
        
        async function testUpload() {
            const fileInput = document.getElementById('fileInput');
            const uploadBtn = document.getElementById('uploadBtn');
            
            const file = fileInput.files[0];
            if (!file) {
                showResult('uploadResult', '❌ Please select a file first', 'error');
                log('❌ No file selected', 'error');
                return;
            }
            
            log(`📁 Selected file: ${file.name} (${file.size} bytes, ${file.type})`, 'info');
            
            uploadBtn.disabled = true;
            uploadBtn.textContent = '⏳ Uploading...';
            showResult('uploadResult', '⏳ Uploading file...', 'loading');
            
            try {
                const formData = new FormData();
                formData.append('file', file);
                
                log('📤 Sending upload request...', 'info');
                
                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData,
                });
                
                log(`📡 Response status: ${response.status}`, 'info');
                
                const data = await response.json();
                log(`📋 Response data: ${JSON.stringify(data, null, 2)}`, 'info');
                
                if (response.ok) {
                    showResult('uploadResult', `
                        ✅ <strong>Upload Successful!</strong><br>
                        <strong>URL:</strong> <a href="${data.url}" target="_blank" style="color: #00ff99;">${data.url}</a><br>
                        <strong>File:</strong> ${data.fileName}<br>
                        <strong>Size:</strong> ${data.size} bytes<br>
                        <strong>Type:</strong> ${data.type}
                    `, 'success');
                    log(`✅ Upload successful: ${data.fileName}`, 'success');
                } else {
                    showResult('uploadResult', `❌ Upload failed: ${data.error}`, 'error');
                    log(`❌ Upload failed: ${data.error}`, 'error');
                    if (data.details) {
                        log(`🔍 Error details: ${data.details}`, 'error');
                    }
                }
                
            } catch (error) {
                showResult('uploadResult', `❌ Network error: ${error.message}`, 'error');
                log(`❌ Network error: ${error.message}`, 'error');
            } finally {
                uploadBtn.disabled = false;
                uploadBtn.textContent = '🚀 Test Upload';
            }
        }
        
        // Initialize on page load
        window.onload = function() {
            log('🚀 Production Upload Test initialized', 'info');
            checkAPI();
        };
    </script>
</body>
</html>
