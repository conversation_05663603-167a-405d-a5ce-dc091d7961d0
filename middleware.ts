import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { createClient } from '@supabase/supabase-js'

// Pages that should be checked for maintenance mode
const MAINTENANCE_PAGES = [
  '/',
  '/top-casinos',
  '/casinos-singapore',
  '/reviews',
  '/list-report',
  '/forum',
  '/guide',
  '/news'
];

export async function middleware(request: NextRequest) {
  // 🔄 URL REDIRECTS: Handle legacy URLs
  const url = request.nextUrl.clone();

  // Redirect /games to /top-casinos
  if (url.pathname === '/games') {
    url.pathname = '/top-casinos';
    return NextResponse.redirect(url, 301); // Permanent redirect
  }

  // Redirect /casinos to /casinos-singapore
  if (url.pathname === '/casinos') {
    url.pathname = '/casinos-singapore';
    return NextResponse.redirect(url, 301); // Permanent redirect
  }

  // 🌐 DOMAIN REDIRECT: Force www.gurusingapore.com (only in production)
  if (process.env.NODE_ENV === 'production') {
    const hostname = request.headers.get('host') || '';

    // Redirect from gurusingapore.com to www.gurusingapore.com
    if (hostname === 'gurusingapore.com') {
      url.host = 'www.gurusingapore.com';
      url.protocol = 'https:';
      return NextResponse.redirect(url, 301); // Permanent redirect
    }
  }

  // Handle CORS for all requests
  const response = NextResponse.next()

  // Skip middleware for auth callback routes and debug pages to prevent redirect loops
  if (request.nextUrl.pathname.startsWith('/auth/callback') ||
      request.nextUrl.pathname.startsWith('/signin') ||
      request.nextUrl.pathname.startsWith('/session-fix') ||
      request.nextUrl.pathname.startsWith('/debug-admin') ||
      request.nextUrl.pathname.startsWith('/debug-session') ||
      request.nextUrl.pathname.startsWith('/fix-admin') ||
      request.nextUrl.pathname.startsWith('/api/')) {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 Skipping middleware for route:', request.nextUrl.pathname);
    }
    return response;
  }

  // 🚀 SIMPLIFIED ADMIN PROTECTION: Use client-side protection instead
  // This prevents middleware session issues
  if (request.nextUrl.pathname.startsWith('/admin')) {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 Admin route accessed, letting client handle auth:', request.nextUrl.pathname);
    }
    // Let the page handle authentication client-side
    return response;
  }

  // 🚀 MAINTENANCE MODE: Handled client-side by MaintenanceWrapper
  // Removed server-side check to prevent duplicate Supabase clients and slow middleware



  // Set CORS headers
  response.headers.set('Access-Control-Allow-Origin', '*')
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, apikey, x-client-info')
  response.headers.set('Access-Control-Allow-Credentials', 'true')

  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    return new NextResponse(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, apikey, x-client-info',
        'Access-Control-Allow-Credentials': 'true',
      },
    })
  }

  // Special handling for auth routes
  if (request.nextUrl.pathname.startsWith('/auth/')) {
    console.log('🔄 Auth middleware:', {
      path: request.nextUrl.pathname,
      method: request.method,
      origin: request.headers.get('origin'),
      referer: request.headers.get('referer')
    })
    
    // Add additional headers for auth routes
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', '0')
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
