{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "types": ["react", "react-dom", "node"], "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./*"], "@/src/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/lib/*"], "@/contexts/*": ["./src/contexts/*"]}, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "next-temp/**/*", "src/**/*", "app/**/*", "types/**/*.d.ts", ".next/types/**/*.ts"], "exclude": ["node_modules", "next-temp", "src/test/**/*"]}