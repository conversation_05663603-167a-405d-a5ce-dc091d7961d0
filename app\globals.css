@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground: #f9fafb;

  /* Casino Design System Variables */
  --casino-primary: #00ff99;
  --casino-primary-dark: #00cc7a;
  --casino-primary-light: #33ffb3;

  --casino-secondary: #8b5cf6;
  --casino-accent: #3b82f6;
  --casino-warning: #f59e0b;

  --casino-bg-primary: #0a0e13;
  --casino-bg-secondary: #0f1419;
  --casino-bg-elevated: #1a1f2e;

  --casino-text-primary: #f9fafb;
  --casino-text-secondary: #d1d5db;
  --casino-text-muted: #9ca3af;

  --casino-border-subtle: #2a3441;
  --casino-border-accent: #374151;

  /* Shadows */
  --casino-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --casino-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --casino-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --casino-shadow-glow: 0 0 20px rgba(0, 255, 153, 0.2);

  /* Radius */
  --casino-radius-sm: 0.375rem;
  --casino-radius-md: 0.5rem;
  --casino-radius-lg: 0.75rem;
  --casino-radius-xl: 1rem;
}

@theme inline {
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground: #f9fafb;
  }
}

body {
  @apply bg-gradient-to-b from-casino-dark via-casino-dark-lighter to-casino-dark text-casino-text-primary font-sans antialiased;
}

/* Modern Design System Utilities */
.glass-effect {
  @apply bg-casino-card-bg/80 backdrop-blur-md border border-casino-border-subtle/50;
}

/* Top Banner Fullscreen Fix */
.top-banner-fullscreen {
  position: relative;
  z-index: 10;
}

.top-banner-fullscreen::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 64px; /* navbar height */
  background: transparent;
  z-index: -1;
}

/* Running Text Ticker Animation - True Seamless Infinite Scroll */
@keyframes scroll-seamless {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-33.333%);
  }
}

.animate-scroll-seamless {
  animation: scroll-seamless 120s linear infinite;
  display: flex;
  width: max-content;
}

/* Mobile faster animation */
@media (max-width: 768px) {
  .animate-scroll-seamless {
    animation: scroll-seamless 80s linear infinite;
  }
}

/* Animation states for pause/resume */
.animate-scroll-seamless.paused {
  animation-play-state: paused;
}

.animate-scroll-seamless.running {
  animation-play-state: running;
}

.neon-glow {
  @apply shadow-[0_0_20px_rgba(0,255,153,0.3)];
}

.neon-glow-hover {
  @apply hover:shadow-[0_0_30px_rgba(0,255,153,0.5)] transition-shadow duration-300;
}

.card-modern {
  @apply bg-casino-card-bg border border-casino-border-subtle rounded-xl shadow-lg backdrop-blur-sm;
}

.card-hover {
  @apply hover:border-casino-border-accent hover:shadow-xl hover:shadow-casino-neon-green/10 transition-all duration-300;
}

.text-gradient {
  @apply bg-gradient-to-r from-casino-neon-green to-casino-neon-blue bg-clip-text text-transparent;
}

.btn-primary {
  @apply bg-casino-neon-green text-casino-dark hover:bg-casino-neon-green-dark hover:shadow-lg hover:shadow-casino-neon-green/25 active:scale-95 transition-all duration-200;
}

.btn-secondary {
  @apply bg-casino-surface text-casino-text-primary border border-casino-border-subtle hover:bg-casino-surface-elevated hover:border-casino-border-accent transition-all duration-200;
}

/* Performance optimizations for mobile devices */
@media (max-width: 767px) {
  /* Disable animations on low-end devices */
  .animate-spin,
  .animate-pulse,
  .animate-ping,
  .animate-bounce,
  .animate-fade-in,
  .animate-fadeIn,
  .animate-fade-in-down,
  .animate-fade-in-up,
  .animate-accordion-up,
  .animate-accordion-down,
  .animate-neon-glow,
  .animate-gradient-shift {
    animation: none !important;
  }

  /* Reduce motion for better performance */
  .transition-all,
  .transition-transform,
  .transition-opacity {
    transition-duration: 0.15s !important;
  }

  /* Optimize transforms for mobile */
  .transform {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* Reduce blur effects on mobile */
  .backdrop-blur-sm { backdrop-filter: blur(2px) !important; }
  .backdrop-blur-md { backdrop-filter: blur(4px) !important; }
  .backdrop-blur-lg { backdrop-filter: blur(6px) !important; }
}

/* Mobile drawer slide animation */
@keyframes slide-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-out-right {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-out forwards;
}

.animate-slide-out-right {
  animation: slide-out-right 0.3s ease-in forwards;
}

/* Swiper Styles */
@layer base {
  @keyframes scroll {
    to {
      transform: translate(calc(-50% - 0.5rem));
    }
  }

  .swiper-button-next,
  .swiper-button-prev {
    color: #4ade80 !important;
    background-color: rgba(26, 31, 46, 0.7);
    width: 44px !important;
    height: 44px !important;
    border-radius: 50%;
    transition: all 0.2s ease-in-out;
  }

  .swiper-button-next:hover,
  .swiper-button-prev:hover {
    background-color: rgba(74, 222, 128, 0.2);
  }

  .swiper-button-next::after,
  .swiper-button-prev::after {
    font-size: 1.25rem !important;
    font-weight: 700;
  }

  .swiper-pagination-bullet {
    background-color: #9ca3af !important;
    opacity: 0.5;
    transition: all 0.2s ease-in-out;
  }

  .swiper-pagination-bullet-active {
    background-color: #4ade80 !important;
    opacity: 1;
    transform: scale(1.2);
  }
}

/* Enhanced Mobile Menu Animations */
@layer utilities {
  /* Fade in animation */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out forwards;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  /* Slide up animation */
  .animate-slide-up {
    animation: slideUp 0.4s ease-out forwards;
  }

  @keyframes slideUp {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  /* Enhanced slide in right animation */
  .animate-slide-in-right {
    animation: slideInRight 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }

  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  /* Enhanced slide out right animation */
  .animate-slide-out-right {
    animation: slideOutRight 0.3s cubic-bezier(0.55, 0.06, 0.68, 0.19) forwards;
  }

  @keyframes slideOutRight {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }

  /* Bounce in animation for buttons */
  .animate-bounce-in {
    animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
  }

  @keyframes bounceIn {
    0% {
      transform: scale(0.3);
      opacity: 0;
    }
    50% {
      transform: scale(1.05);
    }
    70% {
      transform: scale(0.9);
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Pulse glow effect - DISABLED */
  .animate-pulse-glow {
    animation: none;
  }

  /* Shake animation for errors */
  .animate-shake {
    animation: shake 0.5s ease-in-out;
  }

  @keyframes shake {
    0%, 100% {
      transform: translateX(0);
    }
    25% {
      transform: translateX(-5px);
    }
    75% {
      transform: translateX(5px);
    }
  }

  /* Scale bounce for interactive elements */
  .hover-scale {
    transition: transform 0.2s ease-in-out;
  }

  .hover-scale:hover {
    transform: scale(1.05);
  }

  .hover-scale:active {
    transform: scale(0.95);
  }

  /* Synchronized sidebar animations - matches hamburger timing */
  .sidebar-fade-in {
    animation: sidebarFadeIn 0.3s ease-in-out forwards;
  }

  .sidebar-fade-out {
    animation: sidebarFadeOut 0.3s ease-in-out forwards;
  }

  @keyframes sidebarFadeIn {
    from {
      opacity: 0;
      transform: translateX(16px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes sidebarFadeOut {
    from {
      opacity: 1;
      transform: translateX(0);
    }
    to {
      opacity: 0;
      transform: translateX(16px);
    }
  }

  /* Staggered content animation for sidebar sections */
  .sidebar-content-1 {
    animation-delay: 0.05s;
  }

  .sidebar-content-2 {
    animation-delay: 0.1s;
  }

  .sidebar-content-3 {
    animation-delay: 0.15s;
  }

  /* Smooth drawer slide animation - synchronized with hamburger */
  .drawer-slide-in {
    animation: drawerSlideIn 0.3s ease-in-out forwards;
  }

  .drawer-slide-out {
    animation: drawerSlideOut 0.3s ease-in-out forwards;
  }

  @keyframes drawerSlideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes drawerSlideOut {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
}

/* Design System Variables */
@layer base {
  :root {
    --background: 222 22% 7%;
    --foreground: 210 40% 98%;
    --card: 218 25% 12%;
    --card-foreground: 210 40% 98%;
    --popover: 218 25% 12%;
    --popover-foreground: 210 40% 98%;
    --primary: 142 76% 50%;
    --primary-foreground: 222 22% 7%;
    --secondary: 262 83% 70%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 142 76% 50%;
    --radius: 0.5rem;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground relative;
    font-family: 'Inter', system-ui, sans-serif;
    background: linear-gradient(135deg, #0e1117 0%, #11141c 100%);
    min-height: 100vh;
  }

  body::before {
    content: '';
    @apply absolute inset-0 opacity-10 blur-[100px] -z-10;
  }
}

/* Component Utilities */
@layer components {
  .neon-border {
    @apply border border-casino-neon-green/30;
  }

  .neon-text {
    @apply text-casino-neon-green;
  }

  .card-hover {
    @apply transition-all duration-300 hover:scale-105;
  }

  .gradient-text {
    @apply text-casino-neon-green;
  }

  /* Ultra Smooth Hamburger Animations */
  .hamburger-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
  }

  .hamburger-line {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
    will-change: transform, opacity;
  }

  /* Smooth Mobile Menu Transitions */
  .mobile-menu-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, opacity;
  }

  /* Hamburger Hover Effects */
  .hamburger-container:hover .hamburger-line {
    background-color: #4ade80;
  }

  /* Performance Optimizations for Smooth Animations */
  .smooth-transform {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Mobile-optimized components */
  .mobile-card {
    @apply bg-casino-card-bg border border-casino-border-subtle rounded-lg p-4 mobile:p-3;
    @apply mobile:shadow-sm desktop:shadow-lg;
    @apply mobile:backdrop-blur-sm desktop:backdrop-blur-md;
  }

  .mobile-button {
    @apply px-4 py-2 mobile:px-3 mobile:py-2 mobile:text-sm;
    @apply min-h-[44px] mobile:min-h-[40px]; /* Touch target size */
    @apply transition-colors duration-200 mobile:duration-150;
  }

  .mobile-grid {
    @apply grid gap-6 mobile:gap-4;
    @apply grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  .mobile-container {
    @apply container mx-auto px-4 mobile:px-3;
    @apply max-w-7xl mobile:max-w-full;
  }

  .mobile-text {
    @apply text-base mobile:text-sm;
    @apply leading-relaxed mobile:leading-normal;
  }

  .mobile-heading {
    @apply text-2xl mobile:text-xl font-bold;
    @apply mb-4 mobile:mb-3;
  }

  /* Touch-friendly utilities */
  .touch-target {
    @apply min-h-[44px] min-w-[44px]; /* WCAG AA touch target size */
  }

  .touch-scroll {
    @apply overflow-auto;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Performance utilities */
  .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  .will-change-transform {
    will-change: transform;
  }

  .will-change-auto {
    will-change: auto;
  }
}

/* Animations */
@keyframes slide-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}

.animate-slide-left {
  animation: slide-left 30s linear infinite;
}

/* Fix for text visibility issues */
.text-white {
  color: #ffffff !important;
}

.text-gray-400 {
  color: #9ca3af !important;
}

.text-gray-300 {
  color: #d1d5db !important;
}

/* Ensure mobile menu text is visible */
.mobile-menu-text {
  color: #ffffff !important;
}

/* Force text visibility on cards */
.card-text {
  color: #ffffff !important;
}

.card-text-secondary {
  color: #9ca3af !important;
}
