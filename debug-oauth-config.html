<!DOCTYPE html>
<html>
<head>
    <title>OAuth Configuration Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: white; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #333; border-radius: 8px; }
        .success { background: #0f3f0f; border-color: #4CAF50; }
        .error { background: #3f0f0f; border-color: #f44336; }
        .warning { background: #3f3f0f; border-color: #ff9800; }
        .code { background: #2a2a2a; padding: 10px; border-radius: 4px; font-family: monospace; }
        button { background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #45a049; }
    </style>
</head>
<body>
    <h1>🔍 OAuth Configuration Debug</h1>
    
    <div class="section">
        <h2>📋 Current Environment</h2>
        <div class="code">
            <div>Current URL: <span id="currentUrl"></span></div>
            <div>Origin: <span id="origin"></span></div>
            <div>Expected Callback: <span id="expectedCallback"></span></div>
        </div>
    </div>

    <div class="section warning">
        <h2>🔧 Required Supabase Configuration</h2>
        <p><strong>Supabase Dashboard → Authentication → Settings:</strong></p>
        <div class="code">
            Site URL: http://localhost:3000<br>
            Additional redirect URLs:<br>
            - http://localhost:3000/auth/callback<br>
            - https://www.gurusingapore.com/auth/callback
        </div>
    </div>

    <div class="section warning">
        <h2>🔑 Required Google OAuth Configuration</h2>
        <p><strong>Google Cloud Console → APIs & Services → Credentials:</strong></p>
        <div class="code">
            OAuth 2.0 Client → Authorized redirect URIs:<br>
            - https://plhpubcmugqosexcgdhj.supabase.co/auth/v1/callback
        </div>
    </div>

    <div class="section">
        <h2>🧪 Test OAuth Flow</h2>
        <button onclick="testOAuth()">Test Google OAuth</button>
        <div id="testResult" style="margin-top: 10px;"></div>
    </div>

    <div class="section">
        <h2>📝 Configuration Checklist</h2>
        <div id="checklist">
            <div>☐ Supabase Site URL includes localhost:3000</div>
            <div>☐ Supabase redirect URLs include localhost callback</div>
            <div>☐ Google OAuth redirect URI points to Supabase</div>
            <div>☐ Browser cache cleared</div>
            <div>☐ Tried incognito mode</div>
        </div>
    </div>

    <script>
        // Display current environment
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('origin').textContent = window.location.origin;
        document.getElementById('expectedCallback').textContent = window.location.origin + '/auth/callback';

        function testOAuth() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = '<div style="color: #ff9800;">🔄 Testing OAuth configuration...</div>';
            
            // Simulate what would happen in OAuth flow
            const redirectOrigin = window.location.origin;
            const callbackUrl = `${redirectOrigin}/auth/callback`;
            
            resultDiv.innerHTML = `
                <div class="code">
                    <strong>OAuth Flow Test:</strong><br>
                    1. Current Origin: ${redirectOrigin}<br>
                    2. Callback URL: ${callbackUrl}<br>
                    3. Expected Flow: Google → Supabase → ${callbackUrl}<br><br>
                    
                    <strong>If redirecting to production:</strong><br>
                    - Check Supabase Dashboard Site URL<br>
                    - Ensure localhost:3000 is in redirect URLs<br>
                    - Clear browser cache and try incognito mode
                </div>
            `;
        }

        // Auto-run test on load
        setTimeout(testOAuth, 1000);
    </script>
</body>
</html>
