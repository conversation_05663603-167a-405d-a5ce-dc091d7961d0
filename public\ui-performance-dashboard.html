<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 UI/UX Performance Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff;
            padding: 20px;
            margin: 0;
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #667eea;
            text-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
            margin-bottom: 30px;
        }
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.3);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        .card:hover {
            border-color: #667eea;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.2);
        }
        .card h3 {
            color: #667eea;
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        .status-success { background: #4ade80; }
        .status-error { background: #f87171; }
        .status-warning { background: #fbbf24; }
        .status-loading { background: #667eea; animation: pulse 1.5s infinite; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 5px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        .test-results {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            max-height: 400px;
            overflow-y: auto;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .test-success { color: #4ade80; }
        .test-error { color: #f87171; }
        .test-warning { color: #fbbf24; }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .metric-card {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
            padding: 12px;
            text-align: center;
        }
        .metric-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }
        .metric-label {
            font-size: 0.8em;
            opacity: 0.8;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #667eea;
        }
        .error {
            color: #f87171;
            background: rgba(248, 113, 113, 0.1);
            border: 1px solid rgba(248, 113, 113, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .wide-card {
            grid-column: 1 / -1;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .feature-item {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 6px;
            padding: 10px;
            font-size: 0.9em;
        }
        .feature-name {
            font-weight: bold;
            color: #667eea;
        }
        .feature-status {
            float: right;
        }
        .score-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
            margin: 0 auto 10px;
        }
        .score-excellent { background: linear-gradient(45deg, #4ade80, #22c55e); }
        .score-good { background: linear-gradient(45deg, #fbbf24, #f59e0b); }
        .score-poor { background: linear-gradient(45deg, #f87171, #ef4444); }
        .responsive-test {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            flex-wrap: wrap;
        }
        .breakpoint-indicator {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.8em;
            background: rgba(102, 126, 234, 0.2);
            border: 1px solid rgba(102, 126, 234, 0.3);
        }
        .device-simulator {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .device-frame {
            border: 2px solid #667eea;
            border-radius: 8px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.3);
            text-align: center;
            min-width: 100px;
        }
        .device-mobile { width: 120px; height: 200px; border-radius: 15px; }
        .device-tablet { width: 180px; height: 240px; border-radius: 10px; }
        .device-desktop { width: 250px; height: 150px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 UI/UX Performance Dashboard</h1>
        
        <div class="dashboard-grid">
            <!-- Responsive Design Test -->
            <div class="card">
                <h3>
                    <span class="status-indicator status-loading" id="responsiveStatus"></span>
                    Responsive Design
                </h3>
                <div id="responsiveResults">
                    <div class="loading">Testing responsive breakpoints...</div>
                </div>
                <div class="responsive-test">
                    <div class="breakpoint-indicator">📱 Mobile</div>
                    <div class="breakpoint-indicator">📱 Tablet</div>
                    <div class="breakpoint-indicator">🖥️ Desktop</div>
                    <div class="breakpoint-indicator">🖥️ Wide</div>
                </div>
                <button onclick="testResponsiveDesign()">Test Responsiveness</button>
            </div>
            
            <!-- Performance Metrics -->
            <div class="card">
                <h3>
                    <span class="status-indicator status-loading" id="performanceStatus"></span>
                    Performance Metrics
                </h3>
                <div id="performanceResults">
                    <div class="loading">Measuring performance...</div>
                </div>
                <button onclick="measurePerformance()">Measure Performance</button>
            </div>
            
            <!-- Accessibility Score -->
            <div class="card">
                <h3>
                    <span class="status-indicator status-loading" id="accessibilityStatus"></span>
                    Accessibility Score
                </h3>
                <div id="accessibilityResults">
                    <div class="loading">Checking accessibility...</div>
                </div>
                <button onclick="checkAccessibility()">Check Accessibility</button>
            </div>
        </div>
        
        <!-- Device Simulator -->
        <div class="card wide-card">
            <h3>📱 Device Simulator</h3>
            <div class="device-simulator">
                <div class="device-frame device-mobile">
                    <div>📱 Mobile</div>
                    <div style="font-size: 0.8em; margin-top: 5px;">320-767px</div>
                </div>
                <div class="device-frame device-tablet">
                    <div>📱 Tablet</div>
                    <div style="font-size: 0.8em; margin-top: 5px;">768-1023px</div>
                </div>
                <div class="device-frame device-desktop">
                    <div>🖥️ Desktop</div>
                    <div style="font-size: 0.8em; margin-top: 5px;">1024px+</div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 15px;">
                <button onclick="simulateDevice('mobile')">Test Mobile View</button>
                <button onclick="simulateDevice('tablet')">Test Tablet View</button>
                <button onclick="simulateDevice('desktop')">Test Desktop View</button>
            </div>
        </div>
        
        <!-- Comprehensive UI Test -->
        <div class="card wide-card">
            <h3>
                <span class="status-indicator status-loading" id="comprehensiveStatus"></span>
                Comprehensive UI/UX Test
            </h3>
            <div id="comprehensiveResults">
                <div class="loading">Ready for comprehensive testing...</div>
            </div>
            <div style="text-align: center; margin-top: 15px;">
                <button onclick="runComprehensiveTest()" style="font-size: 1.1em; padding: 15px 30px;">
                    🚀 Run All UI/UX Tests
                </button>
            </div>
        </div>
        
        <!-- Core Web Vitals -->
        <div class="card wide-card">
            <h3>⚡ Core Web Vitals Monitor</h3>
            <div id="webVitalsResults">
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-number" id="fcpValue">-</div>
                        <div class="metric-label">FCP (ms)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-number" id="lcpValue">-</div>
                        <div class="metric-label">LCP (ms)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-number" id="fidValue">-</div>
                        <div class="metric-label">FID (ms)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-number" id="clsValue">-</div>
                        <div class="metric-label">CLS</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-number" id="ttfbValue">-</div>
                        <div class="metric-label">TTFB (ms)</div>
                    </div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 15px;">
                <button onclick="measureWebVitals()">Measure Web Vitals</button>
                <button onclick="startRealTimeMonitoring()">Start Real-time Monitoring</button>
            </div>
        </div>
    </div>

    <script>
        let realTimeMonitoring = false;

        // Auto-run comprehensive test on load
        window.addEventListener('load', () => {
            runComprehensiveTest();
            measureWebVitals();
        });

        async function testResponsiveDesign() {
            const statusEl = document.getElementById('responsiveStatus');
            const resultsEl = document.getElementById('responsiveResults');
            
            statusEl.className = 'status-indicator status-loading';
            resultsEl.innerHTML = '<div class="loading">Testing responsive design...</div>';
            
            try {
                const response = await fetch('/api/test-ui-performance');
                const data = await response.json();
                
                const responsiveTest = data.testResults.find(test => test.test === 'Responsive Breakpoints');
                
                if (responsiveTest && responsiveTest.success) {
                    statusEl.className = 'status-indicator status-success';
                    resultsEl.innerHTML = `
                        <div class="test-results">
                            ${responsiveTest.details.breakpoints.map(bp => `
                                <div class="test-item">
                                    <span>${bp.breakpoint}: ${bp.minWidth}-${bp.maxWidth}px</span>
                                    <span class="test-success">✅ Valid</span>
                                </div>
                            `).join('')}
                        </div>
                        <div style="margin-top: 10px; color: #4ade80;">
                            ✅ All ${responsiveTest.details.totalBreakpoints} breakpoints configured correctly
                        </div>
                    `;
                } else {
                    statusEl.className = 'status-indicator status-error';
                    resultsEl.innerHTML = `<div class="error">❌ Responsive design test failed</div>`;
                }
            } catch (error) {
                statusEl.className = 'status-indicator status-error';
                resultsEl.innerHTML = `<div class="error">❌ Test failed: ${error.message}</div>`;
            }
        }

        async function measurePerformance() {
            const statusEl = document.getElementById('performanceStatus');
            const resultsEl = document.getElementById('performanceResults');
            
            statusEl.className = 'status-indicator status-loading';
            resultsEl.innerHTML = '<div class="loading">Measuring performance...</div>';
            
            try {
                const response = await fetch('/api/test-ui-performance');
                const data = await response.json();
                
                const perfTest = data.testResults.find(test => test.test === 'Performance Optimization Features');
                
                if (perfTest && perfTest.success) {
                    statusEl.className = 'status-indicator status-success';
                    resultsEl.innerHTML = `
                        <div class="score-circle score-excellent">
                            ${perfTest.details.implementationRate}
                        </div>
                        <div class="feature-grid">
                            ${perfTest.details.features.slice(0, 6).map(feature => `
                                <div class="feature-item">
                                    <div class="feature-name">${feature.name}</div>
                                    <div class="feature-status">${feature.implemented ? '✅' : '❌'}</div>
                                    <div style="font-size: 0.8em; opacity: 0.8; margin-top: 5px;">${feature.description}</div>
                                </div>
                            `).join('')}
                        </div>
                    `;
                } else {
                    statusEl.className = 'status-indicator status-warning';
                    resultsEl.innerHTML = `<div class="error">⚠️ Performance optimization needs improvement</div>`;
                }
            } catch (error) {
                statusEl.className = 'status-indicator status-error';
                resultsEl.innerHTML = `<div class="error">❌ Performance test failed: ${error.message}</div>`;
            }
        }

        async function checkAccessibility() {
            const statusEl = document.getElementById('accessibilityStatus');
            const resultsEl = document.getElementById('accessibilityResults');
            
            statusEl.className = 'status-indicator status-loading';
            resultsEl.innerHTML = '<div class="loading">Checking accessibility...</div>';
            
            try {
                const response = await fetch('/api/test-ui-performance');
                const data = await response.json();
                
                const a11yTest = data.testResults.find(test => test.test === 'Accessibility Features');
                
                if (a11yTest && a11yTest.success) {
                    statusEl.className = 'status-indicator status-success';
                    const score = parseFloat(a11yTest.details.accessibilityScore);
                    const scoreClass = score >= 90 ? 'score-excellent' : score >= 75 ? 'score-good' : 'score-poor';
                    
                    resultsEl.innerHTML = `
                        <div class="score-circle ${scoreClass}">
                            ${a11yTest.details.accessibilityScore}
                        </div>
                        <div class="feature-grid">
                            ${a11yTest.details.features.slice(0, 6).map(feature => `
                                <div class="feature-item">
                                    <div class="feature-name">${feature.name}</div>
                                    <div class="feature-status">${feature.implemented ? '✅' : '❌'}</div>
                                </div>
                            `).join('')}
                        </div>
                    `;
                } else {
                    statusEl.className = 'status-indicator status-warning';
                    resultsEl.innerHTML = `<div class="error">⚠️ Accessibility features need improvement</div>`;
                }
            } catch (error) {
                statusEl.className = 'status-indicator status-error';
                resultsEl.innerHTML = `<div class="error">❌ Accessibility test failed: ${error.message}</div>`;
            }
        }

        async function runComprehensiveTest() {
            const statusEl = document.getElementById('comprehensiveStatus');
            const resultsEl = document.getElementById('comprehensiveResults');
            
            statusEl.className = 'status-indicator status-loading';
            resultsEl.innerHTML = '<div class="loading">Running comprehensive UI/UX tests...</div>';
            
            try {
                const response = await fetch('/api/test-ui-performance');
                const data = await response.json();
                
                if (data.success) {
                    statusEl.className = 'status-indicator status-success';
                    resultsEl.innerHTML = `
                        <div class="test-results">
                            ${data.testResults.map(test => `
                                <div class="test-item">
                                    <div>
                                        <strong>${test.test}</strong>
                                        ${test.details ? `<div style="font-size: 0.8em; opacity: 0.7;">${test.details.implementationRate || test.details.overallScore || test.details.accessibilityScore || 'Completed'}</div>` : ''}
                                    </div>
                                    <span class="${test.success ? 'test-success' : 'test-error'}">
                                        ${test.success ? '✅ PASS' : '❌ FAIL'}
                                    </span>
                                </div>
                            `).join('')}
                        </div>
                        <div style="margin-top: 15px; color: #4ade80; text-align: center;">
                            🎉 ${data.summary.passedTests}/${data.summary.totalTests} tests passed (${data.summary.successRate})
                        </div>
                    `;
                } else {
                    statusEl.className = 'status-indicator status-error';
                    resultsEl.innerHTML = `<div class="error">❌ ${data.message}</div>`;
                }
            } catch (error) {
                statusEl.className = 'status-indicator status-error';
                resultsEl.innerHTML = `<div class="error">❌ Comprehensive test failed: ${error.message}</div>`;
            }
        }

        async function measureWebVitals() {
            try {
                const response = await fetch('/api/test-ui-performance');
                const data = await response.json();
                
                const webVitalsTest = data.testResults.find(test => test.test === 'Core Web Vitals Simulation');
                
                if (webVitalsTest && webVitalsTest.details) {
                    const metrics = webVitalsTest.details.metrics;
                    document.getElementById('fcpValue').textContent = metrics.fcp;
                    document.getElementById('lcpValue').textContent = metrics.lcp;
                    document.getElementById('fidValue').textContent = metrics.fid;
                    document.getElementById('clsValue').textContent = metrics.cls;
                    document.getElementById('ttfbValue').textContent = metrics.ttfb;
                }
            } catch (error) {
                console.error('Failed to measure Web Vitals:', error);
            }
        }

        function startRealTimeMonitoring() {
            realTimeMonitoring = !realTimeMonitoring;
            
            if (realTimeMonitoring) {
                const interval = setInterval(() => {
                    if (!realTimeMonitoring) {
                        clearInterval(interval);
                        return;
                    }
                    measureWebVitals();
                }, 2000);
            }
        }

        function simulateDevice(deviceType) {
            const sizes = {
                mobile: { width: 375, height: 667 },
                tablet: { width: 768, height: 1024 },
                desktop: { width: 1440, height: 900 }
            };
            
            const size = sizes[deviceType];
            const newWindow = window.open(
                window.location.origin, 
                `${deviceType}_simulator`,
                `width=${size.width},height=${size.height},scrollbars=yes,resizable=yes`
            );
            
            if (newWindow) {
                newWindow.focus();
            }
        }
    </script>
</body>
</html>
