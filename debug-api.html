<!DOCTYPE html>
<html>
<head>
    <title>Debug API</title>
</head>
<body>
    <h1>Debug Casino Reports API</h1>
    <div id="results"></div>

    <script>
        async function debugAPI() {
            const results = document.getElementById('results');
            
            try {
                // Test public endpoint
                console.log('Testing public endpoint...');
                results.innerHTML += '<h2>Public Endpoint (/api/casino-reports)</h2>';
                
                const response = await fetch('/api/casino-reports');
                const data = await response.json();
                
                results.innerHTML += `<p>Status: ${response.status}</p>`;
                results.innerHTML += `<p>Success: ${data.success}</p>`;
                results.innerHTML += `<p>Count: ${data.count}</p>`;
                results.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                
                // Test admin endpoint
                console.log('Testing admin endpoint...');
                results.innerHTML += '<h2>Admin Endpoint (/api/admin/casino-reports)</h2>';
                
                const adminResponse = await fetch('/api/admin/casino-reports');
                const adminData = await adminResponse.json();
                
                results.innerHTML += `<p>Status: ${adminResponse.status}</p>`;
                results.innerHTML += `<p>Success: ${adminData.success}</p>`;
                results.innerHTML += `<p>Count: ${adminData.count}</p>`;
                results.innerHTML += `<pre>${JSON.stringify(adminData, null, 2)}</pre>`;
                
            } catch (error) {
                results.innerHTML += `<p>Error: ${error.message}</p>`;
            }
        }
        
        // Run debug when page loads
        debugAPI();
    </script>
</body>
</html>
