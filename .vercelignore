# Vercel ignore file - exclude files that might cause deployment issues

# Development files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build artifacts that might cause conflicts
.next/
out/
dist/

# Scripts that use file system
scripts/
supabase/

# Config files that might cause issues
next-sitemap.config.*
# vercel.json - REMOVED: We need this for deployment configuration

# Test files
src/test/
**/*.test.*
**/*.spec.*

# Temporary files
*.tmp
*.temp
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
